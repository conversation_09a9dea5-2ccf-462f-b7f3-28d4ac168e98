import pandas as pd
from flask import Flask, jsonify, request
from datetime import datetime
import locale

# --- Configuration and Initialization ---

# Set the locale to Albanian for correct day/month names.
# This may require the 'sq_AL.UTF-8' locale to be installed on your system.
# On Debian/Ubuntu: sudo locale-gen sq_AL.UTF-8 && sudo update-locale
# On other systems, the method may vary.
try:
    locale.setlocale(locale.LC_TIME, 'sq_AL.UTF-8')
except locale.Error:
    print("Warning: 'sq_AL.UTF-8' locale not found. Falling back to default locale.")
    print("Albanian date names may not be displayed correctly.")

app = Flask(__name__)

# --- Helper Functions ---

def load_prayer_data():
    """
    Loads and preprocesses the prayer times data from the CSV file.
    This function is called once when the application starts.
    """
    try:
        # Read the CSV file. The date format 'd-M' (e.g., 1-Jan) is parsed correctly.
        # We parse the 'Date' column as datetime objects.
        df = pd.read_csv('prayer_times.csv', parse_dates=['Date'], date_format='%d-%b')
        
        # When parsed without a year, pandas defaults to 1900.
        # We'll replace the year with the current year to make lookups easier.
        current_year = datetime.now().year
        df['Date'] = df['Date'].apply(lambda dt: dt.replace(year=current_year))
        
        # Set the 'Date' column as the index for fast lookups.
        df.set_index('Date', inplace=True)
        
        # Replace any potential NaN (empty) values with empty strings for cleaner JSON output.
        df.fillna('', inplace=True)
        
        print(f"Successfully loaded and processed prayer times for the year {current_year}.")
        return df
    except FileNotFoundError:
        print("Error: 'prayer_times.csv' not found. Please make sure the file exists.")
        return None

def format_albanian_date(date_obj):
    """Formats a datetime object into a beautiful Albanian string."""
    # Format: "E Hënë, 1 Janar 2024"
    return date_obj.strftime("%A, %d %B %Y")

# --- Load Data on Startup ---
prayer_data = load_prayer_data()

# --- API Endpoints (Routes) ---

@app.route('/')
def index():
    """A simple homepage to show that the API is running and how to use it."""
    return """
    <h1>Prayer Times API</h1>
    <p>Welcome! The API is running.</p>
    <h2>Available Endpoints:</h2>
    <ul>
        <li><code><a href="/api/today">/api/today</a></code> - Get prayer times for today.</li>
        <li><code>/api/date/&lt;day&gt;-&lt;month&gt;</code> - Get prayer times for a specific date (e.g., <code><a href="/api/date/1-1">/api/date/1-1</a></code> for Jan 1st or <code><a href="/api/date/10-4">/api/date/10-4</a></code> for Apr 10th).</li>
    </ul>
    """

@app.route('/api/today', methods=['GET'])
def get_today():
    """Returns prayer times for the current date."""
    if prayer_data is None:
        return jsonify({"error": "Data file not loaded. Check server logs."}), 500

    today = datetime.now().date()
    # Create a full datetime object for today to match the index
    lookup_date = datetime(today.year, today.month, today.day)
    
    try:
        data = prayer_data.loc[lookup_date].to_dict()
        data['data_shqip'] = format_albanian_date(lookup_date)
        return jsonify(data)
    except KeyError:
        return jsonify({"error": "Prayer times for today not found in the data file."}), 404

@app.route('/api/date/<string:date_str>', methods=['GET'])
def get_by_date(date_str):
    """Returns prayer times for a specific date provided in d-M format."""
    if prayer_data is None:
        return jsonify({"error": "Data file not loaded. Check server logs."}), 500
        
    try:
        # Parse the date string (e.g., "1-1" for Jan 1st)
        day, month = map(int, date_str.split('-'))
        current_year = datetime.now().year
        lookup_date = datetime(current_year, month, day)
        
        data = prayer_data.loc[lookup_date].to_dict()
        data['data_shqip'] = format_albanian_date(lookup_date)
        return jsonify(data)
    except (KeyError, ValueError):
        return jsonify({"error": f"Date '{date_str}' not found or invalid format. Use 'day-month' format (e.g., 1-1 or 28-9)."}), 404

# --- Main execution ---
if __name__ == '__main__':
    # debug=True allows for auto-reloading when you save the file.
    # In a production environment, you would use a proper WSGI server like Gunicorn.
    app.run(host='0.0.0.0', port=5000, debug=True)
