import pandas as pd
from flask import Flask, jsonify, request
from datetime import datetime
import locale

# --- Configuration and Initialization ---

# Set the locale to Albanian for correct day/month names.
# This may require the 'sq_AL.UTF-8' locale to be installed on your system.
# On Debian/Ubuntu: sudo locale-gen sq_AL.UTF-8 && sudo update-locale
# On other systems, the method may vary.
try:
    locale.setlocale(locale.LC_TIME, 'sq_AL.UTF-8')
except locale.Error:
    print("Warning: 'sq_AL.UTF-8' locale not found. Falling back to default locale.")
    print("Albanian date names may not be displayed correctly.")

app = Flask(__name__)

# --- Helper Functions ---

def load_prayer_data():
    """
    Loads and preprocesses the prayer times data from the CSV file.
    This function is called once when the application starts.
    """
    try:
        # Read the CSV file without parsing dates first
        df = pd.read_csv('prayer_times.csv')

        # Parse the Date column manually to handle the d-MMM format (e.g., 1-Jan)
        current_year = datetime.now().year

        def parse_date_string(date_str):
            """Parse date string like '1-Jan' or '1-Shk' into a datetime object for current year"""
            try:
                # Albanian month abbreviations mapping to month numbers
                albanian_months = {
                    'Jan': 1,    # January
                    'Shk': 2,    # Shkurt = February
                    'Mar': 3,    # March
                    'Pri': 4,    # Prill = April
                    'Maj': 5,    # Maj = May
                    'Qer': 6,    # Qershor = June
                    'Kor': 7,    # Korrik = July
                    'Gush': 8,   # Gusht = August
                    'Sht': 9,    # Shtator = September
                    'Tet': 10,   # Tetor = October
                    'Nen': 11,   # Nëntor = November
                    'Dhj': 12    # Dhjetor = December
                }

                # Split the date string to get day and month
                day, month_abbr = date_str.split('-')

                # Convert Albanian month abbreviation to month number
                if month_abbr in albanian_months:
                    month_num = albanian_months[month_abbr]
                    # Create datetime object directly using constructor
                    from datetime import datetime as dt
                    parsed_date = dt(current_year, month_num, int(day))
                    return parsed_date
                else:
                    print(f"Unknown month abbreviation: {month_abbr}")
                    return None

            except Exception as e:
                print(f"Error parsing date '{date_str}': {e}")
                return None

        # Apply the parsing function to the Date column
        df['Date'] = df['Date'].apply(parse_date_string)

        # Remove any rows where date parsing failed
        df = df.dropna(subset=['Date'])

        # Set the 'Date' column as the index for fast lookups.
        df.set_index('Date', inplace=True)

        # Replace any potential NaN (empty) values with empty strings for cleaner JSON output.
        df.fillna('', inplace=True)

        print(f"Successfully loaded and processed {len(df)} prayer times for the year {current_year}.")
        return df
    except FileNotFoundError:
        print("Error: 'prayer_times.csv' not found. Please make sure the file exists.")
        return None
    except Exception as e:
        print(f"Error loading prayer data: {e}")
        return None

def format_albanian_date(date_obj):
    """Formats a datetime object into a beautiful Albanian string."""
    # Format: "E Hënë, 1 Janar 2024"
    return date_obj.strftime("%A, %d %B %Y")

# --- Load Data on Startup ---
prayer_data = load_prayer_data()

# --- API Endpoints (Routes) ---

@app.route('/')
def index():
    """A simple homepage to show that the API is running and how to use it."""
    return """
    <h1>Takvimi API</h1>
    <p>Selamun alejkum! API po punon.</p>
    <p>Punuar nga: REMZI NURA.</p>
    <h2>Available Endpoints:</h2>
    <ul>
        <li><code><a href="/api/today">/api/today</a></code> - Get prayer times for today- Merrni vaktet per sot.</li>
        <li><code>/api/date/&lt;day&gt;-&lt;month&gt;</code> - Get prayer times for a specific date- Merrni vaktet per ndonje date specifike (e.g., <code><a href="/api/date/1-1">/api/date/1-1</a></code> for Jan 1st or <code><a href="/api/date/10-4">/api/date/10-4</a></code> for Apr 10th).</li>
    </ul>
    """

@app.route('/api/today', methods=['GET'])
def get_today():
    """Returns prayer times for the current date."""
    if prayer_data is None:
        return jsonify({"error": "Data file not loaded. Check server logs."}), 500

    today = datetime.now().date()
    # Create a full datetime object for today to match the index
    lookup_date = datetime(today.year, today.month, today.day)

    try:
        data = prayer_data.loc[lookup_date].to_dict()
        data['data_shqip'] = format_albanian_date(lookup_date)
        return jsonify(data)
    except KeyError:
        return jsonify({"error": "Prayer times for today not found in the data file."}), 404

@app.route('/api/date/<string:date_str>', methods=['GET'])
def get_by_date(date_str):
    """Returns prayer times for a specific date provided in d-M format."""
    if prayer_data is None:
        return jsonify({"error": "Data file not loaded. Check server logs."}), 500
        
    try:
        # Parse the date string (e.g., "1-1" for Jan 1st)
        day, month = map(int, date_str.split('-'))
        current_year = datetime.now().year
        lookup_date = datetime(current_year, month, day)
        
        data = prayer_data.loc[lookup_date].to_dict()
        data['data_shqip'] = format_albanian_date(lookup_date)
        return jsonify(data)
    except (KeyError, ValueError):
        return jsonify({"error": f"Date '{date_str}' not found or invalid format. Use 'day-month' format (e.g., 1-1 or 28-9)."}), 404

# --- Main execution ---
if __name__ == '__main__':
    import os
    # Use environment PORT for deployment platforms, fallback to 5000 for local development
    port = int(os.environ.get('PORT', 5000))
    # Only enable debug mode in development
    debug_mode = os.environ.get('FLASK_ENV') == 'development'
    app.run(host='0.0.0.0', port=port, debug=debug_mode)
