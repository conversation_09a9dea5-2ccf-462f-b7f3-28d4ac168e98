# Prayer Times API (Takvimiapi)

A Flask web application that serves Albanian prayer times data from a CSV file through a REST API.

## Features

- **Albanian Localization**: Supports Albanian month abbreviations and date formatting
- **REST API**: Simple endpoints to get prayer times
- **Today's Times**: Get current day's prayer times
- **Date-specific**: Get prayer times for any specific date
- **JSON Response**: Clean JSON format with Albanian date strings

## API Endpoints

- `GET /` - Homepage with API documentation
- `GET /api/today` - Get today's prayer times
- `GET /api/date/{day}-{month}` - Get prayer times for specific date (e.g., `/api/date/1-6` for June 1st)

## Local Development

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run the application:
   ```bash
   python app.py
   ```

3. Access the API at `http://localhost:5000`

## Deployment Options

### 1. Render (Recommended - Free)

1. Push your code to GitHub
2. Go to [render.com](https://render.com)
3. Create a new Web Service
4. Connect your GitHub repository
5. <PERSON><PERSON> will automatically detect the Flask app and deploy it

### 2. Heroku

1. Install Heroku CLI
2. Login: `heroku login`
3. Create app: `heroku create your-app-name`
4. Deploy: `git push heroku main`

### 3. Railway

1. Go to [railway.app](https://railway.app)
2. Connect your GitHub repository
3. Railway will automatically deploy your Flask app

### 4. PythonAnywhere

1. Upload files to PythonAnywhere
2. Configure web app in dashboard
3. Set up WSGI configuration

## Data Format

The application reads from `prayer_times.csv` with Albanian month abbreviations:
- Jan, Shk (Shkurt), Mar, Pri (Prill), Maj, Qer (Qershor), Kor (Korrik), Gush (Gusht), Sht (Shtator), Tet (Tetor), Nen (Nëntor), Dhj (Dhjetor)

## Response Format

```json
{
  "Imsaku": "04:30",
  "Sabahu": "05:45",
  "Lindja": "07:15",
  "Dreka": "13:20",
  "Ikindia": "16:45",
  "Akshami": "19:30",
  "Jacia": "21:00",
  "Festat": "",
  "Shenime": "",
  "data_shqip": "Sunday, 30 June 2025"
}
```

## Environment Variables

- `PORT`: Port number (automatically set by deployment platforms)
- `FLASK_ENV`: Set to 'development' for debug mode

## License

This project is open source and available under the MIT License.
